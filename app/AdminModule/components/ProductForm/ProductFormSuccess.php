<?php


namespace SuperKoderi\Admin\Components;


use App\Model\Mutation;
use App\Model\Orm;
use App\Model\ParameterValue;
use App\Model\Product;
use App\Model\ProductFile;
use App\Model\ProductImage;
use App\Model\ProductLocalization;
use App\Model\ProductLocalizationModel;
use App\Model\ProductModel;
use App\Model\ProductProduct;
use App\Model\ProductTree;
use App\Model\ProductVariant;
use App\Model\ProductVariantModel;
use App\Model\ProductVariantPrice;
use App\Model\Supply;
use App\Model\TreeModel;
use App\Model\TreeProduct;
use App\Model\User;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\CustomField\CustomFields;
use SuperKoderi\MoneyHelper;

class ProductFormSuccess
{
	public function __construct(
		private readonly Orm $orm,
		private readonly ProductModel $productModel,
		private readonly TreeModel $treeModel,
		private readonly ProductLocalizationModel $productLocalizationModel,
		private readonly ProductVariantModel $productVariantModel,
		private readonly CustomFields $customFields
	) {}

	/**
	 * @param ICollection|Mutation[] $mutations
	 */
	public function execute(Form $form, Product $product, User $user, ArrayHash $values, ICollection $mutations): void
	{
		$data = $form->getHttpData();


		$this->handleCommmonProduct($product, $user, $values->productCommon);
		$this->handleLocalizationsProduct($product, (array) $values->productLocalizations, (array) $data);
		$this->handleVariants($product, $values, $user);

		if (isset($data['productCommon']['images'])) {
			$this->handleImagesVariants($product, (array) $data['productCommon']['images']);
		}


		$this->orm->product->persistAndFlush($product);
		$this->checkPublicParameter($product);
		$this->orm->product->persistAndFlush($product);


		if ($product->isVoucher === 0) {
			$this->handleParameters((array) $data, $product);
		}

		$this->handleSetups($product, $values);
		$this->handleInternalName($product);
		$this->productModel->handleFlags($product);
		$this->orm->product->persistAndFlush($product);





		$this->productModel->saveToEs($product);

	}


	private function handleCommmonProduct(Product $product, User $userEntity, ArrayHash $commonProductFormData): void
	{
		$product->internalName = $commonProductFormData->internalName;


		if ($product->isVoucher === 0) {
			$product->isSet = (int)$commonProductFormData->isSet;
			$product->isMikrosvin = (int)$commonProductFormData->isMikrosvin;
			$product->isNew = (int)$commonProductFormData->isNew;
			$product->storeLimit = (int)$commonProductFormData->storeLimit;
			$product->isAction = (int)$commonProductFormData->isAction;
			$product->isIconic = (int)$commonProductFormData->isIconic;
			$product->isSale = (int)$commonProductFormData->isSale;
		}

		if (!$commonProductFormData->isNewExpirationTime) {
			$now = new DateTimeImmutable();
			$interval = new \DateInterval('P60D');
			$after60Days = $now->add($interval);
			$commonProductFormData->isNewExpirationTime = $after60Days;
		}

		$product->isNewExpirationTime = $commonProductFormData->isNewExpirationTime;


		if ($commonProductFormData->publicFrom) {
			$product->publicFrom = $commonProductFormData->publicFrom;
		}
		if ($commonProductFormData->publicTo) {
			$product->publicTo = $commonProductFormData->publicTo;
		}
		$product->edited = $userEntity->id;
		$product->editedTime = new DateTimeImmutable();

		if (isset($commonProductFormData->availableServices)) {
			$product->availableServices = implode("|", $commonProductFormData->availableServices);
		} else {
			$product->availableServices = NULL;
		}

		$this->handleProducts($product, $commonProductFormData);
		$this->handleVats($product, $commonProductFormData);
		$this->handleProductTagsParent($product, $commonProductFormData);
		$this->handleImages($product, $commonProductFormData);


		$this->orm->persistAndFlush($product);
	}


	private function handleLocalizationsProduct(Product $product, array $localizations, array $data): void
	{
		foreach ($localizations as $mutationId => $localizationData) {
			$localization = $product->productLocalizations->toCollection()->getBy(['mutation->id' => $mutationId]);
			if ($localization) {
				$localization->name = $localizationData->name;
				$localization->annotation = $localizationData->annotation;
				$localization->content = $localizationData->content;
				$localization->nameTitle = $localizationData->nameTitle;
				$localization->nameAnchor = $localizationData->nameAnchor;
				$localization->description = $localizationData->description;
				// save alias history
				$localization->setAliasHistoryString($localizationData->aliasHistory);
				// than solve alais
				$localization->setAlias($localizationData->alias);

				$localization->public = (int)$localizationData->public;


				$this->handleVoucher($localization, isset($data['productLocalizations'][$mutationId]['vouchers']) ? $data['productLocalizations'][$mutationId]['vouchers'] : []);
				$this->handleForcedCategory($localization);
				if (isset($localizationData->categories)) {
					$this->handleCategories($localization, $localizationData->categories);
				}
				if (isset($localizationData->videos)) {
					$this->handleVideos($localization, $localizationData->videos);
				}

				if (isset($localizationData->links)) {
					$this->handleLinks($localization, $localizationData->links);
				}

				if (isset($localizationData->pages)) {
					$this->handlePages($localization, $localizationData->pages);
				}

				if (isset($localizationData->files)) {
					$this->handleFiles($localization, $localizationData->files);
				}

				if (isset($data['customFields'][$localization->mutation->langCode]) && $data['customFields'][$localization->mutation->langCode]) {
					$localization->cf = $this->customFields->prepareDataToSave($data['customFields'][$localization->mutation->langCode]);
				}

				$setup = new \stdClass();
				$setup->inheritCategories = $localizationData->setup->inheritCategories;
				$setup->inheritVideos = $localizationData->setup->inheritVideos;
				$setup->inheritFiles = $localizationData->setup->inheritFiles;
				$setup->inheritPages = $localizationData->setup->inheritPages;
				$setup->inheritLinks = $localizationData->setup->inheritLinks;

				$localization->setup = ArrayHash::from((array)$setup);
			}
		}
	}


	private function handleVariants(Product $product, ArrayHash $values, User $user): void
	{
		$tmpIds = [];

		if (isset($values->variants)) {
			//update items
			if ($values->variants) {
				$sort = 0;
				foreach ($values->variants as $variantId=>$variantData) {
					if ($variantId === 'newItemMarker') {
						continue;
					}

					if (is_int($variantId)) {
						$variant = $this->orm->productVariant->getById($variantId);
						$variant->edited =  new DateTimeImmutable();
						$variant->editedBy = $user->id;
					} else {
						$variant = $this->productVariantModel->createWithLocalizations($product);
						$variant->created =  new DateTimeImmutable();
						$variant->createdBy = $user->id;
					}
					$variant->sort = $sort;
					$sort++;

					$this->handleVariantCommon($variant, $variantData);
					$this->handleVariantLocalizations($variant, $variantData);
					$this->handleVariantPrices($variant, $variantData);
					$this->handleVariantSupplies($variant, $variantData);

					$this->orm->productVariant->persistAndFlush($variant);
					$tmpIds[] = $variant->id;
				}
			}
		}

		foreach ($product->variants as $variant) {
			if (!in_array($variant->id, $tmpIds)) {
				$this->orm->productVariant->remove($variant);
			}
		}
	}


	private function handleVariantCommon(ProductVariant $variant, ArrayHash $variantData): void
	{
		if (isset($variantData->variantCommon)) {
			$variant->ean = $variantData->variantCommon->ean;
			$variant->code = $variantData->variantCommon->code;

			$variant->param1Value =  isset($variantData->variantCommon->param1Value) ? $variantData->variantCommon->param1Value : null;
			$variant->param2Value =  isset($variantData->variantCommon->param2Value) ? $variantData->variantCommon->param2Value : null;
		}
	}

	private function handleVariantLocalizations(ProductVariant $variant, ArrayHash $variantData): void
	{
		if (isset($variantData->variantLocalizations)) {
			foreach ($variantData->variantLocalizations as $mutationId=>$variantLocalizationData) {

				$productVariantLocalization = $variant->variantLocalizations->toCollection()->getBy(['mutation->id' => $mutationId]);
				if ($productVariantLocalization) {
					$productVariantLocalization->active = 1;//(int)$variantLocalizationData->active;
				}
			}
		}
	}


	private function handleVariantPrices(ProductVariant $variant, ArrayHash $variantData): void
	{
		if (isset($variantData->variantPrices)) {
			foreach ($variantData->variantPrices as $mutationId => $variantPrices) {
				foreach ($variantPrices as $priceLevelId => $priceData) {
					if (isset($variant->pricesByLevel[$mutationId][$priceLevelId])) {
						$productVariantPrice = $variant->pricesByLevel[$mutationId][$priceLevelId];
						$productVariantPrice->price = MoneyHelper::getNormalizedFloat($priceData->price, ProductVariantPrice::PRICE_ROUND_PRECISION);
					} else {
						$productVariantPrice = new ProductVariantPrice();
						$this->orm->productVariantPrice->attach($productVariantPrice);
						$productVariantPrice->mutation = $mutationId;
						$productVariantPrice->priceLevel = $priceLevelId;
						$productVariantPrice->productVariant = $variant;
						$productVariantPrice->productId = $variant->product->id;
						$productVariantPrice->price = MoneyHelper::getNormalizedFloat($priceData->price, ProductVariantPrice::PRICE_ROUND_PRECISION);
					}
				}
			}
		}
	}

	private function handleVariantSupplies(ProductVariant $variant, ArrayHash $variantData): void
	{
		if (isset($variantData->variantSupplies)) {
			foreach ($variantData->variantSupplies as $stockId => $supplyData) {
				if (isset($variant->suppliesByStock[$stockId])) {
					$supply = $variant->suppliesByStock[$stockId];
				} else {
					$supply = new Supply();
					$this->orm->supply->attach($supply);
					$supply->stock = $stockId;
					$supply->variant = $variant;
				}
				$supply->amount = (int)$supplyData->amount;
			}
		}
	}

	private function handleVideos(ProductLocalization $localization, ArrayHash $videosData): void
	{
		$videos = [];
		foreach ($videosData as $videoData) {
			if ($videoData->link) {
				$videos[] = ['name' => $videoData->name, 'link' => $videoData->link];
			}
		}
		$localization->videos = ArrayHash::from($videos);
	}


	private function handleLinks(ProductLocalization $localization, ArrayHash $linksData): void
	{
		$links = [];
		foreach ($linksData as $linkData) {
			if ($linkData->link) {
				$links[] = ['name' => $linkData->name, 'link' => $linkData->link, 'open' => $linkData->open];
			}
		}
		$localization->links = ArrayHash::from($links);
	}


	private function handleProducts(Product $product, ArrayHash $commonProductFormData): void
	{
		$containerName = 'products';
		$type = ($product->isSet === 1) ? ProductProduct::TYPE_SET : ProductProduct::TYPE_NORMAL;

		if (isset($commonProductFormData->$containerName)) {
			$this->handleRelations($containerName, $type, $product, $commonProductFormData);
		}
	}

	private function handleVats(Product $product, ArrayHash $commonProductFormData): void
	{
		if (isset($commonProductFormData->vats)) {
			$vats = [];
			foreach ($commonProductFormData->vats as $stateId => $vatData) {
				$vats[$stateId] = $vatData->vatRate;
			}
			$product->vats = $vats;
		}
	}

	private function handleProductTagsParent(Product $product, ArrayHash $values): void
	{
		if (property_exists($values, 'productTagParents') && $values->productTagParents !== null) {
			$product->productTagParents->set([]);
			foreach ($values->productTagParents as $productTagParentData) {
				if ($productTagParentData->id) {
					$productTagParent = $this->orm->productTagParent->getById($productTagParentData->id);
					if ($productTagParent) {
						$product->productTagParents->add($productTagParent);
					}
				}
			}
		}
	}

	private function handleForcedCategory(ProductLocalization $productLocalization): void
	{
		$pages = $productLocalization->getMutation()->pages;
		$this->productLocalizationModel->attachTo($productLocalization, [$pages->eshop->id]);
	}


	private function handleCategories(ProductLocalization $productLocalization, ArrayHash $categoriesFormData): void
	{
		$treeIdForLocalization = [];
		foreach ($categoriesFormData as $categoryKey=>$categoryFormData) {
			if ($categoryKey !== 'newItemMarker' && $categoryFormData->id) {
				$treeIdForLocalization[] = $categoryFormData->id;
			}
		}


		$this->productLocalizationModel->attachTo($productLocalization, $treeIdForLocalization);
	}


	private function handleRelations(string $name, string $type, Product $product, ArrayHash $commonProductFormData): void
	{

		$productProducts = $this->orm->productProduct->findBy([
			'type' => $type,
			'mainProduct' => $product,
		]);

		$attachedProductIds = $productProducts->fetchPairs('id', 'attachedProduct->id');

		if ($commonProductFormData->$name) {
			$sort = 0;
			foreach ($commonProductFormData->$name as $attachedProductKey=>$attachedProductData) {
				if ($attachedProductData->id) {
					$attachedProduct = $this->orm->product->getById($attachedProductData->id);
					if ($attachedProduct) {

						if (($key = array_search($attachedProduct->id, $attachedProductIds)) !== false) {
							unset($attachedProductIds[$key]);
							$this->orm->productProduct->replace($product, $attachedProduct, $type, $sort);
						} else {
							$newProductProduct = $this->orm->productProduct->createNew();
							$this->orm->productProduct->update($newProductProduct, $product, $attachedProduct, $sort, $type);
						}
						$sort++;
					}
				}
			}
		}


		// remove old
		foreach ($attachedProductIds as $productProductId => $attachedProductId) {
			$productProductToDelete = $this->orm->productProduct->getById($productProductId);
			if ($productProductToDelete !== null) {
				$this->orm->productProduct->removeAndFlush($productProductToDelete);
			}
		}
	}


	private function handlePages(ProductLocalization $localization, ArrayHash $pagesData): void
	{
		$actualPages = $localization->pages->fetchPairs('id', null);

		$count = 0;
		foreach ($pagesData as $pageKey=>$pageData) {
			if ($pageData->id) {
				$tree = $this->orm->tree->getById($pageData->id);
				if (is_int($pageKey)) {
					unset($actualPages[$pageData->id]);
				}
				$treeProduct = $this->orm->treeProduct->replace($localization->product, $tree, TreeProduct::TYPE_NORMAL_TO_PRODUCT, $count);
				$this->orm->persist($treeProduct);
				$count++;
			}
		}

		// remove
		foreach ($actualPages as $tree) {
			$treeProductToDelete = $this->orm->treeProduct->getBy([
				'type' => TreeProduct::TYPE_NORMAL_TO_PRODUCT,
				'product' => $localization->product,
				'tree' => $tree
			]);
			$this->orm->treeProduct->remove($treeProductToDelete);
		}
	}


	private function handleFiles(ProductLocalization $localization, ArrayHash $filesData): void
	{
		$filesToDelete = $localization->files->toCollection()->fetchPairs('this->file->id', null);

		$sort = 0;
		foreach ($filesData as $fileKey=>$fileDataRow) {
			if ($fileKey === 'newItemMarker') {
				continue;
			}
			if ($fileDataRow->fileId) {

				if (is_int($fileKey)) {
					$productFile = $localization->files->toCollection()->getBy(['this->file->id' => $fileDataRow->fileId]);
					if ($productFile) {
						$productFile->name = $fileDataRow->fileName;
						$productFile->sort = $sort;
						$productFile->size = $productFile->size;
					}

					unset($filesToDelete[$fileDataRow->fileId]);
				} else {
					$file = $this->orm->file->getById($fileDataRow->fileId);
					if ($file) {
						$productFile = new ProductFile();
						$this->orm->productFile->attach($productFile);
						$productFile->name = $fileDataRow->fileName;
						$productFile->sort = $sort;
						$productFile->size = (string) $file->size;
						$productFile->url = $file->url;
						$productFile->file = $file->id;

						$localization->files->add($productFile);
					}
				}

				if (isset($productFile)) {
					$this->orm->persist($productFile);
					$sort++;
				}

			}
		}

		// remove
		foreach ($filesToDelete as $productFile) {
			$this->orm->productFile->remove($productFile);
		}

	}

	private function handleSetups(Product $product, ArrayHash $values): void
	{
		$defaultLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $this->orm->mutation->getDefault()]);

		foreach ($product->productLocalizations as $productLocalization) {
			if ($productLocalization->id == $defaultLocalization->id) {
				continue;
			}
			$setup = $productLocalization->setup;

			// inheritVideos
			if (isset($setup->inheritVideos) && $setup->inheritVideos) {
				$productLocalization->videos = $defaultLocalization->videos;
			}

			// inheritLinks
			if (isset($setup->inheritLinks) && $setup->inheritLinks) {
				$productLocalization->links = $defaultLocalization->links;
			}

			// inheritPages
			if (isset($setup->inheritPages) && $setup->inheritPages) {
				$newPages = [];
				foreach ($defaultLocalization->pagesAll as $sisterPage) {
					$newPage = $this->treeModel->findMySisterPageInMutations($sisterPage, $productLocalization->mutation);
					if ($newPage) {
						$newPages[] = $newPage;
					}
				}

				$count = 0;
				$newPageIds = [];
				foreach ($newPages as $pageKey=>$newPage) {
					$treeProduct = $this->orm->treeProduct->replace($productLocalization->product, $newPage, TreeProduct::TYPE_NORMAL_TO_PRODUCT, $count);
					$this->orm->persist($treeProduct);
					$count++;
					$newPageIds[] = $newPage->id;
				}

				$treeProductsToDelete = $this->orm->treeProduct->findBy([
					'type' => TreeProduct::TYPE_NORMAL_TO_PRODUCT,
					'product' => $productLocalization->product,
					'tree->rootId' => $productLocalization->mutation->rootId,
					'tree!=' => $newPageIds
				]);

				foreach ($treeProductsToDelete as $treeProductToDelete) {
					$this->orm->treeProduct->removeAndFlush($treeProductToDelete);
				}
			}

			// inheritCategorie
			if (isset($setup->inheritCategories) && $setup->inheritCategories) {
				$defaultCategories = $product->productTrees->toCollection()->findBy([
					'tree->rootId' => $defaultLocalization->mutation->rootId,
				]);
				$currentCategoriesByTreeId = $product->productTrees->toCollection()->findBy([
					'tree->rootId' => $productLocalization->mutation->rootId,
				])->fetchPairs('tree->id', null);


				foreach ($defaultCategories as $defaultCategory) {
					$newPage = $this->treeModel->findMySisterPageInMutations($defaultCategory->tree, $productLocalization->mutation);
					if ($newPage) {

						if (isset($currentCategoriesByTreeId[$newPage->id])) {
							unset($currentCategoriesByTreeId[$newPage->id]);
						} else {
							$newProductTree = new ProductTree();
							$newProductTree->product = $product;
							$newProductTree->tree = $newPage;
							$newProductTree->sort = $defaultCategory->sort;
							$this->orm->productTree->persistAndFlush($newProductTree);

						}

					}
				}

				foreach ($currentCategoriesByTreeId as $item) {
					$this->orm->productTree->removeAndFlush($item);
				}

			}


			if (isset($setup->inheritFiles) && $setup->inheritFiles) {

				$defaultProductFiles = $defaultLocalization->files->toCollection()->fetchPairs('file->id', null);
				$productFiles = $productLocalization->files->toCollection()->fetchPairs('file->id', null);

				foreach ($defaultProductFiles as $fileId=>$defaultProductFile) {
					/** @var ProductFile $defaultProductFile */
					if (isset($productFiles[$fileId])) {
						$updatedProductFile = $productFiles[$fileId];
						unset($productFiles[$fileId]);
					} else {
						$updatedProductFile = new ProductFile();
						$this->orm->productFile->attach($updatedProductFile);
						$updatedProductFile->productLocalization = $productLocalization;
					}
					$updatedProductFile->name = $defaultProductFile->name;
					$updatedProductFile->url = $defaultProductFile->url;
					$updatedProductFile->size = $defaultProductFile->size;
					$updatedProductFile->sort = $defaultProductFile->sort;
					$updatedProductFile->file = $defaultProductFile->file;

					$this->orm->persistAndFlush($updatedProductFile);
				}


				$fileIdsToDelete = array_keys($productFiles);
				if ($fileIdsToDelete) {

					$productFilesToDelete = $this->orm->productFile->findBy([
						'productLocalization' => $productLocalization,
						'file->id' => $fileIdsToDelete
					]);
					foreach ($productFilesToDelete as $item) {
						$this->orm->productFile->removeAndFlush($item);
					}
				}

			}


		}

		$this->orm->persistAndFlush($product);
	}

	private function handleInternalName(Product $product): void
	{
		$defaultLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $this->orm->mutation->getDefault()]);
		$product->internalName = $defaultLocalization->name;
		$this->orm->persistAndFlush($product);


	}

	private function handleImages(Product $product, ArrayHash $commonProductFormData): void
	{
		$productImagesToDelete = $product->images->toCollection()->fetchPairs('image', null);
		if (isset($commonProductFormData->images)) {
			$sort = 0;
			foreach ($commonProductFormData->images as $imageKey=>$imageData) {
				if ($imageKey === 'newItemMarker') {
					continue;
				}

				$image = $this->orm->libraryImage->getById($imageData->imageId);
				if (is_int($imageKey)) {
					$productImage = $product->images->toCollection()->getBy(['image' => $imageKey]);
					unset($productImagesToDelete[$productImage->image]);
				} else {
					$productImage = new ProductImage();
				}

				$productImage->image = $image->id;
				$productImage->url = $image->url;
				$productImage->sort = $sort;
				$productImage->product = $product;

				$data = [];
				foreach ($product->productLocalizations as $productLocalization) {
					$langCode = $productLocalization->mutation->langCode;
					$data[$langCode]['name'] = $imageData->$langCode->name;
				}
				$productImage->data = $data;

				$sort++;
			}

			foreach ($productImagesToDelete as $productImageToDelete) {
				$this->orm->remove($productImageToDelete);
			}
		}

	}

	private function handleImagesVariants(Product $product, array $images): void
	{
		foreach ($images as $iKey=>$image) {
			if ($iKey === 'newItemMarker') {
				continue;
			}

			if (strpos($iKey, 'newItemMarker_image_') !== false) {
				$imageId = (int) substr($iKey, strlen('newItemMarker_image_'));
				$origImage = $this->orm->libraryImage->getById($imageId);
			} else {
				$origImage = $this->orm->libraryImage->getById($iKey);
				$imageId = $iKey;
			}

			if (is_int($iKey)) {
				$productImage = $product->images->toCollection()->getBy(['image' => $iKey]);
			} else {
				$productImage = $product->images->toCollection()->getBy(['image' => $imageId]);
			}

			if ($productImage) {
				if ($origImage) {
					$productImage->image = $origImage->id;
					$productImage->url = $origImage->url;
				} else {
					$productImage->image = $imageId;
				}
				$productImage->product = $product;

				if (isset($image['variants'])) {
					$productImage->variants = implode('|', $image['variants']);
				} else {
					$productImage->variants = '';
				}
			}
		}
	}

	private function handleParameters(array $data, Product $product): void
	{
		$selectedParameterValuesIds = [];
		if (isset($data['parameterValue'])) {
			foreach ($data['parameterValue'] as $parameterId => $parameterData) {
				$parameter = $this->orm->parameter->getById($parameterId);

				if ($parameter->isSimple) {
					$simpleParameterValue = $parameterData;
					if ($simpleParameterValue !== '') {
						$parameterValue = $parameter->options->toCollection()->getBy(['internalValue' => $simpleParameterValue]);
						if (!$parameterValue) {
							$parameterValue = new ParameterValue();
							$parameterValue->parameter = $parameter;
							$parameterValue->internalValue = $simpleParameterValue;
							$this->orm->parameterValue->persistAndFlush($parameterValue);
							$parameterValue->internalAlias = (string) $parameterValue->id;
							$this->orm->parameterValue->persistAndFlush($parameterValue);
						}

						$selectedParameterValuesIds[] = $parameterValue->id;
						$res = $this->orm->product->addParameterValue($product, $parameterValue);
					}
				} else {
					// select and multiselect
					if (is_array($parameterData)) {
						foreach ($parameterData as $multiselectValueId) {
							$parameterValue = $parameter->options->toCollection()->getById($multiselectValueId);
							if ($parameterValue) {
								$selectedParameterValuesIds[] = $parameterValue->id;
								$this->orm->product->addParameterValue($product, $parameterValue);
							}
						}
					} else {
						$selectValueId = $parameterData;
						$parameterValue = $parameter->options->toCollection()->getById($selectValueId);
						if ($parameterValue) {
							$selectedParameterValuesIds[] = $parameterValue->id;
							$this->orm->product->addParameterValue($product, $parameterValue);
						}
					}
				}
			}
		}

		// add properties parameter values
		// will be solved later

		$propertiesParameter = $this->orm->parameter->getBy(['uid' => 'properties']);
		$selectedParameterValuesIds = array_merge($selectedParameterValuesIds, $propertiesParameter->options->toCollection()->fetchPairs(null, 'id'));


		$this->orm->product->removeMissingParameterValuesIds($product, $selectedParameterValuesIds);
	}

	private function checkPublicParameter(Product $product): void
	{
		$mutationWithVariant = [];
		foreach ($product->variants as $variant) {
			foreach ($variant->variantLocalizations as $variantLocalization) {
				if ($variantLocalization->active === 1) {
					$mutationWithVariant[$variantLocalization->mutation->id] = $variantLocalization->mutation->id;
				}
			}
		}

		foreach ($product->productLocalizations as $productLocalization) {
			if (!isset($mutationWithVariant[$productLocalization->mutation->id])) {
				$productLocalization->public = 0;
			}
		}
	}



	private function handleVoucher(ProductLocalization $localization, array $vouchers): void
	{
		$localization->voucher = null;
		foreach ($vouchers as $voucher) {
			if (isset($voucher['id'])) {
				$localization->voucher = $voucher['id'];

			}
		}
	}

}
