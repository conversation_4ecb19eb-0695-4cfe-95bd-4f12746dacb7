{formContainer $imageContainer}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Alternativní text',
		open: true,
		id: 'image-alt-'.$imageId,
		tags: [
			[text: 'Lokalizované']
		]
	], templates=>$templates}
		{block content}
			{foreach $mutations as $mutation}
				{var $langCode = $mutation->langCode}

				<div class="js-lang js-lang--{$langCode}">
					{include $templates.'/part/core/inp.latte',
						props: [
							input: $imageContainer[$langCode]['name'],
							value: '{imageAlt}',
							label: $langCode,
							isNew: $imageContainer->name == 'newItemMarker',
							classesLabel: ['tag'],
						]
					}
				</div>
			{/foreach}
		{/block}
	{/embed}

	{include $templates.'/part/core/inp.latte',
	props: [
	input: $imageContainer['variants'],
	value: '{imageAlt}',
	label: $imageContainer['variants']->label,
	dataInp: $imageContainer['variants'],
	multiple: true,
	type: 'select',
	isNew: $imageContainer->name == 'newItemMarker',
	]
	}
{/formContainer}
