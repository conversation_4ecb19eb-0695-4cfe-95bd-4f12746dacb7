<?php declare(strict_types = 1);


namespace SuperKoderi\Admin\Components;

use App\Model\ElasticSearch\Product\Facade;
use App\Model\Mutation;
use App\Model\Orm;
use App\Model\Product;
use App\Model\ProductLocalization;
use App\Model\ProductVariantLocalization;
use App\Model\ProductVariantModel;
use App\Model\User;
use Dibi\Connection;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Http\Request;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\ConfigService;
use SuperKoderi\CustomField\SuggestUrls;
use SuperKoderi\hasImageResizerTrait;
use SuperKoderi\LinkFactory;
use SuperKoderi\MutationHolder;
use SuperKoderi\Translator;
use SuperKoderi\TranslatorDB;

/**
 * @property-read DefaultTemplate $template
 */
class ProductForm extends Control
{

	use hasImageResizerTrait;

	public const SUBMIT_MUTATION_CREATE = 'mutationCreate';
	public const SUBMIT_MUTATION_REMOVE = 'mutationRemove';

	private Product $product;

	private Translator $translator;

	private ConfigService $configService;

	private Orm $orm;

	/** @var ICollection|Mutation[] */
	private ICollection $mutations;

	private ProductFormBuilder $productFormBuilder;

	private ProductFormSuccess $productFormSuccess;

	private User $userEntity;

	private mixed $method;

	private array $postData = [];

	private Connection $db;

	private ProductVariantModel $productVariantModel;

	private LinkFactory $linkFactory;

	private MutationHolder $mutationHolder;

	private Mutation $defaultMutation;

	private TranslatorDB $translatorDB;

	private SuggestUrls $urls;


	public function __construct(
		Product $product,
		User $userEntity,
		SuggestUrls $urls,
		Orm $orm,
		LinkFactory $linkFactory,
		ProductVariantModel $productVariantModel,
		Translator $translator,
		TranslatorDB $translatorDB,
		MutationHolder $mutationHolder,
		ProductFormBuilder $productFormBuilder,
		ProductFormSuccess $productFormSuccess,
		ConfigService $configService,
		Connection $db,
		private Facade $productElasticFacade,
		private \App\Model\ElasticSearch\All\Facade $allElasticFacade,
	)
	{
		$this->product = $product;
		$this->translator = $translator;
		$this->translatorDB = $translatorDB;
		$this->configService = $configService;
		$this->orm = $orm;
		$this->productFormBuilder = $productFormBuilder;
		$this->productFormSuccess = $productFormSuccess;

		$this->onAnchor[] = $this->init(...);
		$this->userEntity = $userEntity;
		$this->db = $db;
		$this->productVariantModel = $productVariantModel;
		$this->linkFactory = $linkFactory;
		$this->mutationHolder = $mutationHolder;
		$this->urls = $urls;
	}


	private function init(): void
	{
		$this->method = $this->getPresenter()->request->getMethod();

		if ($this->method === Request::POST) {
			$this->postData = $this->getPresenter()->request->getPost();
		}

		$this->mutations = $this->orm->mutation->findAll();
		$this->defaultMutation = $this->orm->mutation->getDefault();

		//normalize product

		if (!$this->product->variants->count()) {
			$variantShell = $this->productVariantModel->createEmpty($this->userEntity->id);
			$this->product->variants->set([$variantShell]);
			$this->orm->product->persistAndFlush($this->product);
		}

		foreach ($this->mutations as $mutation) {

			$productLocalization = $this->product->productLocalizations->toCollection()->getBy(['mutation' => $mutation]);
			if (!$productLocalization) {
				$newProductLocalization = new ProductLocalization();
				$newProductLocalization->mutation = $mutation;
				$newProductLocalization->product = $this->product;
			}

			foreach ($this->product->variants as $variant) {

				$variantLocalization = $variant->variantLocalizations->toCollection()->getBy(['mutation' => $mutation]);
				if (!$variantLocalization) {
					$newProductVariantLocalization = new ProductVariantLocalization();
					$newProductVariantLocalization->mutation = $mutation;
					$newProductVariantLocalization->variant = $variant;
					$this->orm->variantLocalization->persistAndFlush($newProductVariantLocalization);
				}
			}
		}

		$this->orm->persistAndFlush($this->product);
	}


	public function render(): void
	{
		$sql = "SELECT parentId
				FROM product_parameter tp JOIN parameter p ON (tp.parameterId=p.id)
				WHERE tp.productId='{$this->product->id}'
				GROUP BY parentId";

		$this->template->catsIdWithParamValue = $this->db->query($sql)->fetchPairs();
		$this->template->object = $this->product;
		$this->template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);

		$this->template->paramCats = $this->template->paramCats = $this->orm->parameter->getCats('product');
		$this->template->product = $this->product;
		$this->template->mutations = $this->mutations;
		$this->template->priceLevels = $this->orm->priceLevel->findAll();
		$this->template->stocks = $this->orm->stock->findAll();

		$this->template->orm = $this->orm;
		$this->template->translatorDB = $this->translatorDB;
		$this->template->fileUploadLink = $this->presenter->link('upload!', ['id' => $this->product->id]);

		$this->template->add('imageResizer', $this->imageResizer);

		$this->template->config = $this->configService->getParams();
		$this->template->userEntity = $this->userEntity;

		$linksToFront = [];
		foreach ($this->mutations as $mutation) {
			$this->mutationHolder->setMutation($mutation);
			$linksToFront[$mutation->langCode] = $this->linkFactory->linkTranslateToNette($this->product->getLocalization($mutation), ['show' => 1]);
		}

		$this->mutationHolder->setMutation($this->defaultMutation);
		$this->template->linksToFront = ($linksToFront);

		$this->template->defaultMutation = $this->defaultMutation;

		$this->template->productLocalizationByMutations = $this->product->productLocalizations->toCollection()->fetchPairs('mutation->id', null);

		$this->template->defaultMutation = $this->orm->mutation->getDefault();
		$this->template->setTranslator($this->translator);

		$this->template->urls = $this->urls;
		$this->template->render(__DIR__ . '/productForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$this->productFormBuilder->build($form, $this->product, $this->mutations, $this->postData);

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$this->productFormSuccess->execute($form, $this->product, $this->userEntity, $values, $this->mutations);
		$this->presenter->redirect('edit', ['id' => $this->product->id]);
	}


	public function handleDelete(): void
	{
		$this->allElasticFacade->deleteNow($this->product);
		$this->productElasticFacade->deleteFromAllMutationNow($this->product);
		$this->orm->product->removeAndFlush($this->product);

		$this->presenter->redirect('Catalog:');
	}

}


interface IProductFormFactory
{

	public function create(Product $product, User $userEntity): ProductForm;

}
