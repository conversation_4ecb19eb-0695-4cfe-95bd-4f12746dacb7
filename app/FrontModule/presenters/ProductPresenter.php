<?php

namespace FrontModule;

use App\Model\CatalogTree;
use App\Model\ElasticSearch\Product\ProductGroup;
use App\Model\Product;
use App\Model\ProductLocalization;
use App\Model\ProductVariant;
use App\Model\UserFavoriteProduct;
use Nette\DI\Attributes\Inject;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\BucketFilter\CatalogParameter;
use SuperKoderi\Components\IMessageForFormFactory;
use SuperKoderi\Components\IProductDeliveryInfoFactory;
use SuperKoderi\Components\IProductParametersFactory;
use SuperKoderi\Components\MessageForForm;
use SuperKoderi\Components\ProductDeliveryInfo;
use SuperKoderi\Components\ProductParameters;
use SuperKoderi\Components\WatchdogForm;
use SuperKoderi\Components\WatchdogFormFactory;

/**
 * @method ProductVariant getObject()
 */
class ProductPresenter extends BasePresenter
{
	private ?ProductVariant $variant;

	#[Inject]
	public IMessageForFormFactory $messageForFormFactory;

	#[Inject]
	public IProductDeliveryInfoFactory $productDeliveryInfoFactory;

	#[Inject]
	public IProductParametersFactory $productParametersFactory;

	#[Inject]
	public WatchdogFormFactory $watchdogFormFactory;

	#[Inject]
	public CatalogParameter $catalogParameter;

	private Product $product;

	private ProductLocalization $productLocalization;

	public function __construct(
		private readonly ProductGroup $productGroup,
	)
	{
		parent::__construct();
	}

	public function actionDetail(ProductLocalization $object, mixed $v = null): void
	{
		$variantId = $v;
		$this->productLocalization = $object;
		$this->product = $this->productLocalization->product;

		if ($variantId !== null) {
			$this->variant = $this->orm->productVariant->getById($variantId);
			if ($this->variant->product->id != $this->product->id) {
				$this->redirect($this->product);
			}

		} else {
			$this->variant = $object->getFirstActiveVariantByMutation($this->mutation);
		}


		if ($this->variant === null && $this->getParameter('show') == 1) {
			$this->variant = $this->product->firstVariant;
		}

		$this->setObject($this->productLocalization);
	}


	public function renderDetail(ProductLocalization $object): void
	{
		if ($this->configService->getParam('shop', 'enableLastVisited')) { // init handleLastVisited() je az v beforeRender, nelze volat v actionDetail()
			$this->visitedProduct->add($this->getObject());
		}

		$this->productLocalization = $object;
		$this->product = $this->productLocalization->product;

		// sluzby
		$this->template->specialServices = $this->basket->getSpecialServices($this->product);

		$this->template->product = $this->product;
		$this->template->productLocalization = $this->productLocalization;
		$this->template->variant = $this->variant;

		if (!$this->product->isSet && !$this->product->isVoucher) {
			$this->template->similarProducts = $this->getSimilarProducts($this->product);
		}
	}


	public function actionPreorder(int $id): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(FALSE);
		}
	}

	// ******************* KOMPONENTY ***********************************

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentProductParameters(string $name = "", array $params = []): ProductParameters
	{
		return $this->productParametersFactory->create($this->product);
	}


	protected function createComponentProductDeliveryInfo(): ProductDeliveryInfo
	{
		return $this->productDeliveryInfoFactory->create($this->getObject());
	}

	protected function createComponentWatchdogForm(): WatchdogForm
	{
		return $this->watchdogFormFactory->create($this->variant, $this->currentState, $this->priceLevel, $this->mutationHolder->getMutation());
	}

	public function handleToggleFavoriteProduct(): void
	{
		if ($this->userEntity) {
			$userFavoriteProduct = $this->userEntity->userFavoriteProducts->toCollection()->getBy(["product->id" => $this->product->id]);
			if ($userFavoriteProduct !== null) {
				$this->orm->removeAndFlush($userFavoriteProduct);
			} else {
				$favoriteProduct = new UserFavoriteProduct();
				$favoriteProduct->user = $this->userEntity;
				$favoriteProduct->product = $this->product;
				$this->orm->persistAndFlush($favoriteProduct);
			}
		}
		$this->redrawControl("addProductToFavorite");
	}


	private function getSimilarProducts(Product $product): ICollection
	{
		return $this->productGroup->findSimilarProducts($product, $this->currentState, $this->priceLevel, $this->mutationHolder->getMutation());
	}
}
