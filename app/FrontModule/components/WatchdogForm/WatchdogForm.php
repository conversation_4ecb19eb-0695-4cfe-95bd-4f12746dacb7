<?php declare(strict_types = 1);

namespace SuperKoderi\Components;

use App\Model\Mutation;
use App\Model\PriceLevel;
use App\Model\ProductVariant;
use App\Model\ProductVariantPrice;
use App\Model\ProductVariantWatchdog;
use App\Model\Orm;
use App\Model\State;
use Nette\Application\UI;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use SuperKoderi\Email\Common;
use SuperKoderi\Email\ICommonFactory;
use SuperKoderi\hasAntispamInputTrait;
use SuperKoderi\hasError500CatcherTrait;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\MutationHolder;
use SuperKoderi\TranslatorDB;
use Tracy\Debugger;

/**
 * @property-read DefaultTemplate $template
 */
class WatchdogForm extends UI\Control
{
    use hasMessageForFormComponentTrait;
    use hasAntispamInputTrait;
    use hasError500CatcherTrait;

    public function __construct(
        private readonly ProductVariant $productVariant,
		private readonly State $state,
		private readonly PriceLevel $priceLevel,
		private readonly Mutation $mutation,
        private readonly TranslatorDB $translator,
        private readonly MutationHolder $mutationHolder,
        private readonly Orm $orm,
    )
    {
    }

    protected function createComponentForm(): Form
    {
        $form = new Form;
        $form->setTranslator($this->translator);

        $watchTypes = [
            ProductVariantWatchdog::TYPE_IN_STOCK => 'watchdog_type_in_stock',
			ProductVariantWatchdog::TYPE_ACTION_PRICE => 'watchdog_type_price_drop',
			ProductVariantWatchdog::TYPE_PRICE_UNDER => 'watchdog_type_price_under'
        ];

        $form->addRadioList('type', 'watchdog_type_label', $watchTypes)
            ->setRequired();

        $form->addEmail('email', 'form_label_email')
            ->setRequired();

        $form->addText('price', 'watchdog_price_label')
            ->setHtmlType('number')
            ->addRule(Form::FLOAT, 'watchdog_price_invalid');

        $form->addCheckbox('agree', 'form_label_agree')
            ->setRequired();

        $form->addSubmit('send', 'btn_send');

        $form->onSuccess[] = [$this, 'formSucceeded'];
        $form->onValidate[] = [$this, 'formValidate'];
        $form->onError[] = [$this, 'formError'];
        $this->antispamPrepare($form);

        return $form;
    }

    public function formError(Form $form): void
    {
        if ($this->presenter->isAjax()) {
            $this->redrawControl();
        }
    }

    public function formValidate(Form $form, $values): void
    {
        /** @var ProductVariantPrice $price */
        $price = $this->orm->productVariantPrice->getBy([
            'productVariant' => $this->productVariant,
            'mutation' => $this->mutation,
            'priceLevel' => $this->priceLevel
        ]);

        if($values->type === ProductVariantWatchdog::TYPE_PRICE_UNDER && !$price) {
            $form->addError('watchdog_price_under_invalid');
        }

        if($values->type === ProductVariantWatchdog::TYPE_PRICE_UNDER && $values->price < $price->price) {
            $form->addError('watchdog_price_under_invalid');
        }

        if($values->type === ProductVariantWatchdog::TYPE_PRICE_UNDER && $values->price <= 0) {
            $form->addError('watchdog_price_under_invalid');
        }

        if($values->type === ProductVariantWatchdog::TYPE_IN_STOCK && $this->productVariant->isInStock) {
            $form->addError('watchdog_in_stock_invalid');
        }

        if($values->type === ProductVariantWatchdog::TYPE_ACTION_PRICE && !$this->productVariant->product->isAction) {
            $form->addError('watchdog_action_price_invalid');
        }
    }

    public function formSucceeded(Form $form, ArrayHash $values): void
    {
        try {
            $existing = $this->orm->productVariantWatchdog->getBy([
                'productVariant' => $this->productVariant,
                'email' => $values->email,
                'type' => $values->type,
                'active' => true
            ]);

            if ($existing) {
                $this->flashMessage('watchdog_already_exists', 'warning');
            } else {
                $watchdog = new ProductVariantWatchdog();
                $watchdog->productVariant = $this->productVariant;
                $watchdog->email = $values->email;
                $watchdog->type = $values->type;
                $watchdog->targetPrice = $values->type === 'price_under' ? (float)$values->price : null;
                $watchdog->productVariantPrice = $values->type === 'price_under' ? $this->orm->productVariantPrice->getBy([
                        'productVariant' => $this->productVariant,
                        'mutation' => $this->mutation,
                        'priceLevel' => $this->priceLevel
                    ]) : null;
                $watchdog->active = true;

                $this->orm->productVariantWatchdog->persistAndFlush($watchdog);

                $this->flashMessage('watchdog_created_success', 'ok');
            }
        } catch (\Throwable $e) {
            Debugger::log($e, 'error');
            $this->flashMessage('watchdog_error', 'error');
        }

        if ($this->presenter->isAjax()) {
            $form->setValues([
                'email' => '',
                'price' => '',
                'agree' => false
            ], false);
            $this->redrawControl();
        } else {
            $this->presenter->redirect('this');
        }
    }

    public function render(): void
    {
        try {
            $this->template->setTranslator($this->translator);
            $this->template->productVariant = $this->productVariant;
            $this->template->pages = $this->mutationHolder->getMutation()->pages;
            $this->template->render(__DIR__ . '/watchdogForm.latte');
        } catch (\Throwable $e) {
            $this->handleRenderError500($e);
        }
    }
}
