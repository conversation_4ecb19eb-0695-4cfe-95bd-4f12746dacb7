<!-- TODO-MEZSUP-10 -->
{default $snippetSuffix = ""}
{snippet form}

    {php $control['form']->action .= "#frm-watchdogForm-form"}

    {form form class: 'f-watchdog block-loader', data-naja: '', novalidate: "novalidate"}

        {control messageForForm, $flashes, $form}

        <div class="f-watchdog__content">
            <h3 class="f-watchdog__title">{_'watchdog_form_title'}</h3>
            <p class="f-watchdog__description">{_'watchdog_form_description'} {$productVariant->product->name} - {$productVariant->name}</p>

            <div class="grid grid--y-0">
                <div class="grid__cell size--12-12">
                    {foreach $form['type']->items as $value => $label}
                        {var $isDisabled = false}
                        {if $value === App\Model\ProductVariantWatchdog::TYPE_IN_STOCK && $productVariant->product->isInStock}
                            {var $isDisabled = true}
                        {/if}
                        {if $value === App\Model\ProductVariantWatchdog::TYPE_ACTION_PRICE && $productVariant->product->isAction}
                            {var $isDisabled = true}
                        {/if}
                        <input n:name="type" type="radio" id="frm-watchdogForm-form-type-{$value}" value="{$value}" {if $value === $form['type']->value} checked="checked"{/if} {if $isDisabled}disabled="disabled"{/if}>
                        <label for="frm-watchdogForm-form-type-{$value}">{_$label}</label>
                    {/foreach}
                </div>

                <div class="grid__cell size--12-12" data-controller="WatchdogPrice" data-watchdog-price-target="container">
                    {include '../inp.latte', form: $form, name: price, labelLang: 'watchdog_price_label', type: 'number', pClass: 'u-hide', if: true}
                </div>

                <div class="grid__cell size--12-12">
                    {include '../inp.latte', form: $form, name: email, labelLang: 'form_label_email'}
                </div>
            </div>

            {include '../inp.latte', form: $form, name: agree, agreeLabel: true, type: 'checkbox', labelReplace: "", if: isset($form['agree'])}

            <p class="f-watchdog__btn">
                <button type="submit" class="btn">
                    <span class="btn__text">
                        {_btn_send}
                    </span>
                </button>
            </p>
        </div>

        {*ANTISPAM*}
        {if isset($form['antispamNoJs'])}
            <p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="Antispam">
                <label n:name="antispamNoJs">
                    {_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
                </label>
                <span class="inp-fix">
                    <input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
                </span>
            </p>
        {/if}
        {*/ANTISPAM*}

        <div class="block-loader__loader"></div>

    {/form}

{/snippet}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeRadios = document.querySelectorAll('input[name="type"]');
    const priceContainer = document.querySelector('[data-watchdog-price-target="container"]');

    function togglePriceField() {
        const selectedType = document.querySelector('input[name="type"]:checked');
        if (selectedType && selectedType.value === 'price_under') {
            priceContainer.classList.remove('u-hide');
            priceContainer.querySelector('input[name="price"]').required = true;
        } else {
            priceContainer.classList.add('u-hide');
            priceContainer.querySelector('input[name="price"]').required = false;
        }
    }

    typeRadios.forEach(radio => {
        radio.addEventListener('change', togglePriceField);
    });

    togglePriceField();
});
</script>
