	services:

		# Security
		authenticator: SuperKoderi\Security\Authenticator
		acl: SuperKoderi\Security\Acl
		user: SuperKoderi\Security\User

#		odkomentovani: vypne cache
		cacheStorage:
			class: Nette\Caching\Storages\DevNullStorage

#		cacheStorage:
#			class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

		translatorDB:
			class: SuperKoderi\TranslatorDB


		translator:
			class: SuperKoderi\Translator(%config.adminLang%, %config%)

		configService: SuperKoderi\ConfigService(%config%)

		database:
			class: Dibi\Connection(%database%)

		routerFactory: App\Router\RouterFactory
		router: @routerFactory::createRouter

		menuService:
			class: SuperKoderi\MenuService

		imageResizer: SuperKoderi\ImageResizer(%config.imageSizes%)

		- SuperKoderi\IFileLockFactory

		- \Curl\Curl
		- \SuperKoderi\IVisitedProductFactory

		- \SuperKoderi\IProductCompareFactory
		- \SuperKoderi\Basket

		- \App\Model\ProductReviewModel
		- \App\Model\TreeModel
		- App\Model\Erp\MssqlCaller(%config.env%)
		- \App\Model\PriceLevelModel
		- \App\Model\CatalogTreeModel
		- \App\Model\FileModel
		- \App\Model\OrderModel
		- \App\Model\OrderItemModel
		- App\Model\AliasModel(adminAlias: %config.adminAlias%)
		- \App\Model\BannerModel
		- \App\Model\AliasHistoryModel
		- \App\Model\UserModel
		- \App\Model\UserHashModel
		- \App\Model\LibraryImageModel
		- \App\Model\LibraryTreeModel
		- \App\Model\ProductVariantModel
		- \App\Model\ProductModel
		- \App\Model\ProductLocalizationModel
		- \App\Model\ProductVariantLocalizationModel
		- App\Model\ProductVariantWatchdogModel

		- \SuperKoderi\Model\Pdf\IGeneratePdfFactory
		- \SuperKoderi\ILangRedirectorFactory
		- \SuperKoderi\IPagesFactory
		- \SuperKoderi\Parameters
		- \SuperKoderi\IBasketItemFactory

		- \App\Model\EmailTemplateModel
		- \App\Model\ParameterModel
		- \App\Model\ParameterValueModel
		- \SuperKoderi\Heureka
		- \SuperKoderi\Template\Creator
		- \SuperKoderi\DbalLog("%appDir%/../nettelog/", "mysql")
		- \SuperKoderi\LastVisitCookie
		- \SuperKoderi\EasyMessages
		- \App\Model\BasketItemModel
		- \App\Model\HolidayModel
		- \App\Model\StateModel
		- \App\Model\Orm\String\StringModel

		# Feed
		- \SuperKoderi\Feed\IHeurekaFactory
		- \SuperKoderi\Feed\IGoogleFactory
		- \SuperKoderi\Feed\IZboziFactory
		- \SuperKoderi\Feed\IHeurekaStockFactory

		- App\Model\GroupModel
		- App\Model\Link\LinkSeo
		- SuperKoderi\Model\Router\LinkChecker
		- \SuperKoderi\Console\Model\Sitemap
		- \SuperKoderi\Console\Model\Robots
		- \SuperKoderi\OrderMailService
		- App\Model\Router\Filter
		- App\Model\Router\FilterLang
		- App\Model\Router\FilterAlias
		- App\Model\Router\FilterCommonParameters
		- App\Model\Router\FilterFilterParams
		- App\Model\Router\FilterSeoLink
		- App\Model\Router\FilterVariantId


		- \SuperKoderi\LinkFactory
		- Nette\Http\UrlScript
		- App\Model\RedirectModel
		- App\Model\PlaceModel
		- \SuperKoderi\ImageResizerWrapper
		- App\Model\MutationModel
		- SuperKoderi\MutationsHolder
		- SuperKoderi\MutationHolder
		- SuperKoderi\MutationDetector
		- App\Model\Url\UrlChecker
		- App\Model\Sentry\SentryLogger

		- SuperKoderi\CustomField\ILazyValueFactory

		- App\Model\NewsletterEmailModel
		- App\Model\OrmCleaner
		- App\Model\Erp\Connector\SqlsrvDriver
		-
			factory: \SuperKoderi\ParamText
			inject: true

		- App\Model\SeoLinkModel
		- App\Model\SeoLinkLocalizationFacade


#		EMAILY
		- \SuperKoderi\Mailer\Base

		- \SuperKoderi\Email\ICommonFactory
		- \SuperKoderi\Email\IOrderConfirmationFactory
		- \SuperKoderi\Email\ISyncInfoFactory
		- \SuperKoderi\Email\ISendVoucherFactory

		nette.latteFactory:
			setup:
			- addFilter(timeAgoInWords, [App\Infrastructure\Latte\Filters, timeAgoInWords])
			- addFilter(plural, [App\Infrastructure\Latte\Filters, plural])
			- addFilter(niceDate, [App\Infrastructure\Latte\Filters, niceDate])
			- addFilter(price, [App\Infrastructure\Latte\Filters, price])
			- addFilter(priceFormat, [App\Infrastructure\Latte\Filters, priceFormat])
			- addFilter(texy, [App\Infrastructure\Latte\Filters, texy])
			- addFilter(skDate, [App\Infrastructure\Latte\Filters, skDate])
			- addFilter(prepareStrJs, [App\Infrastructure\Latte\Filters, prepareStrJs])
			- addFilter(clear, [App\Infrastructure\Latte\Filters, clear])
			- addFilter(copyright, [App\Infrastructure\Latte\Filters, copyright])
			- addFilter(icon, [App\Infrastructure\Latte\Filters, icon])
			- addFilter(lineExploder, [App\Infrastructure\Latte\Filters, lineExploder])
			- addFilter(exploder, [App\Infrastructure\Latte\Filters, exploder])
			- addFilter(stock, [App\Infrastructure\Latte\Filters, stock])
			- addFilter(phoneFormat, [App\Infrastructure\Latte\Filters, phoneFormat])
			- addFilter(formatNumberPrecision, [App\Infrastructure\Latte\Filters, formatNumberPrecision])
#			- setTempDirectory("") # vypnuti cache u latte
