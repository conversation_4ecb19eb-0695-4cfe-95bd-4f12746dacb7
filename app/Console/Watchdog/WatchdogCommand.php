<?php declare(strict_types = 1);

namespace App\Console\Watchdog;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\Mutation;
use App\Model\Orm;
use App\Model\ProductVariantWatchdog;
use App\Model\ProductVariantWatchdogModel;
use SuperKoderi\MutationHolder;
use SuperKoderi\MutationsHolder;
use SuperKoderi\TranslatorDB;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'watchdog:send',
	description: 'Send message from watchdog',
)]
class WatchdogCommand extends Command
{

	private Mutation $mutation;

	public function __construct(
		protected Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly Repository $esProductRepository,
		private readonly TranslatorDB $translator,
		private readonly ProductVariantWatchdogModel $watchdogModel,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->mutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
		$this->translator->setMutation($this->mutation);

		$actionProductsToSend = $productsToSend = $relatedProductsToSend = [];

		// Action products
		$actionProductsToCheck = $this->orm->productVariantWatchdog->findProductIds(ProductVariantWatchdog::TYPE_ACTION_PRICE);
		$output->writeln('Checking ' . count($actionProductsToCheck) . ' action products');
		foreach ($this->esProductRepository->findByIds($actionProductsToCheck, $this->mutation, true, true, 1000) as $product) {
			$actionProductsToSend[] = $product->id;
		}

		// Main products
		$productIdsToCheck = $this->orm->productVariantWatchdog->findProductIds(ProductVariantWatchdog::TYPE_IN_STOCK);
		$output->writeln('Checking ' . count($productIdsToCheck) . ' products');
		foreach ($this->esProductRepository->findByIds($productIdsToCheck, $this->mutation, true, false, 1000) as $product) {
			$productsToSend[] = $product->id;
		}

		// Related products
		$relatedProductIdsToCheck = $this->orm->productVariantWatchdog->findRelatedProductIds($productIdsToCheck);
		$output->writeln('Checking ' . count($relatedProductIdsToCheck) . ' related products');
		foreach ($this->esProductRepository->findByIds(array_keys($relatedProductIdsToCheck), $this->mutation, true, false,1000) as $product) {
			$mainProductId = $relatedProductIdsToCheck[$product->id] ?? null;
			$relatedProductsToSend[$mainProductId] = $product->id;
		}

		$sentActionPrice = $this->sendWatchdog($output, $actionProductsToSend, 'actionPriceWatchdog');
		$sent = $this->sendWatchdog($output, $productsToSend);
		$sentRelated = $this->sendRelatedWatchdog($output, $relatedProductsToSend);

		$output->writeln(sprintf('Sent %d action price %d products and %d related products', $sentActionPrice, $sent, $sentRelated));
		$output->writeLn('DONE');
		return 1;
	}

	private function sendWatchdog(OutputInterface $output, array $productsToSend, string $template = 'watchdog'): int
	{
		$i = 0;
		foreach ($this->orm->productVariantWatchdog->findBy(['productVariant->product->id' => $productsToSend, 'type' => ProductVariantWatchdog::TYPE_IN_STOCK]) as $watchdog) {
			try {
				$this->watchdogModel->send($watchdog, $this->mutation, null, $template);
				$this->orm->productVariantWatchdog->remove($watchdog);
				$i++;
			} catch (\Throwable $e) {
				// do nothing
				$output->writeln($e->getMessage());
			}
		}

		$this->orm->productVariantWatchdog->flush();
		return $i;
	}

	private function sendRelatedWatchdog(OutputInterface $output, array $relatedProductsToSend): int
	{
		$i = 0;
		/** @var ProductVariantWatchdog $watchdog */
		foreach ($this->orm->productVariantWatchdog->findBy(['productVariant->product->id' => array_keys($relatedProductsToSend), 'type' => ProductVariantWatchdog::TYPE_IN_STOCK]) as $watchdog) {
			$relatedId = $relatedProductsToSend[$watchdog->productVariant->product->id];
			if (!in_array($relatedId, $watchdog->relatedSended, true)) {
				try {
					$this->watchdogModel->sendRelated($watchdog, $this->mutation, $relatedId);

					$sent                  = $watchdog->relatedSended ?? [];
					$sent[]                = $relatedId;
					$watchdog->relatedSended = $sent;
					$this->orm->productVariantWatchdog->persist($watchdog);
					$i++;
				} catch (\Throwable $e) {
					// do nothing
					$output->writeln($e->getMessage());
				}
			}

		}
		$this->orm->productVariantWatchdog->flush();
		return $i;
	}

}
