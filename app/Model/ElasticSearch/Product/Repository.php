<?php
namespace App\Model\ElasticSearch\Product;

use App\Model\CatalogTree;
use App\Model\EsIndexRepository;
use App\Model\Mutation;
use App\Model\ParameterValue;
use App\Model\PriceLevel;
use App\Model\Product;
use App\Model\State;
use Elastica\Aggregation\Terms;
use Elastica\Query;
use Elastica\Query\Range;
use Elastica\QueryBuilder;
use Elastica\ResultSet;

use Nette\Utils\Random;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\BucketFilter\Sort;


class Repository extends \App\Model\ElasticSearch\Repository
{

	public function __construct(
		private EsIndexRepository $esIndexRepository
	)
	{
	}


	public function searchByFilter(Mutation $mutation, CatalogTree $catalogTree, array $filter, int $size = 1): ResultSet
	{
		$query = new Query();
		$query->setSize($size)
			->setFrom(0);

		$b = new QueryBuilder();

		$must = [];
		$must = $this->addOnlyPublic($b, $must);

		$must = $this->addInPath($catalogTree, $must);


		if ($must !== []) {
			$bool = $b->query()->bool();
			foreach ($must as $item) {
				$bool->addMust($item);
			}

			$query->setQuery($bool);
		}

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->baseSearch($esIndex, $query);
	}

	public function fulltextSearch(Mutation $mutation, array $must, string $q, int $size, array $inPaths, int $offset, Sort $sort): ResultSet
	{
		$query = new Query();
		$query->setSize($size)
			->setFrom($offset);

		$fields = [];
		$fields[] = 'name.dictionary^80';
		$fields[] = 'annotation.dictionary^40';
		$fields[] = 'content.dictionary^20';

		$b = new QueryBuilder();

		foreach ($inPaths as $inPath) {
			$must = $this->addInPath($inPath, $must);
		}

		$must = $this->addOnlyPublic($b, $must);

		$must[] = $b->query()->multi_match()
			->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
			->setQuery($q)
			->setFuzziness(3)
			->setOperator('OR')
			->setFields($fields);

		$filterBuilder = $b->query()->bool();
		foreach ($must as $item) {
			$filterBuilder->addMust($item);
		}

		$query->setQuery($filterBuilder);

		foreach ($sort->getSentences() as $key => $direction) {
			$query->addSort([
				$key => $direction
			]);
		}

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->baseSearch($esIndex, $query);
	}



	// FE search
	public function search(Mutation $mutation = null, string $q, int $size = 30): ResultSet
	{
		$query = new Query();
		$query->setSize($size)
			->setFrom(0);

		$fields = [];
		$fields[] = "name.dictionary^80";
		$fields[] = "annotation.dictionary^40";
		$fields[] = "content.dictionary^40";

		$b = new QueryBuilder();

		$must = [];

		// !!!
		$must = $this->addOnlyPublic($b, $must);
		//$must[] = $b->query()->range('publicFrom');


		$must[] = $b->query()->multi_match()
			->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
			->setQuery($q)
			->setFuzziness(3)
			->setOperator('OR')
			->setFields($fields);

		$bool = $b->query()->bool();
		foreach ($must as $item) {
			$bool->addMust($item);
		}

		$query->setQuery($bool);

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->baseSearch($esIndex, $query);

	}

	public function findProductsAggByCategory(Mutation $mutation, CatalogTree $pathObject): ResultSet
	{
		$query = new Query();
		$query->setSize(1)
			->setFrom(0);

		$b = new QueryBuilder();

		$must = [];

		// !!!
		$must = $this->addOnlyPublic($b, $must);
		$must = $this->addInPath($pathObject, $must);

		if ($must !== []) {
			$bool = $b->query()->bool();
			foreach ($must as $item) {
				$bool->addMust($item);
			}

			$query->setQuery($bool);
		}

		$catagoryAgg = new Terms('categories');
		$catagoryAgg->setField('categories');
		$catagoryAgg->setSize(10000);

		$query->addAggregation($catagoryAgg);

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->baseSearch($esIndex, $query);
	}

	/**
	 * @param QueryBuilder $b
	 * @param array $must
	 * @return array
	 */
	private function addOnlyPublic(QueryBuilder $b, array $must): array
	{
// !!!
		$must[] = $b->query()->term(['isPublic' => true]);
		$publicFrom = new Range('publicFrom', [
			'lte' => 'now'
		]);
		$must[] = $publicFrom;

		$publicTo = new Range('publicTo', [
			'gte' => 'now',
		]);
		$must[] = $publicTo;
		return $must;
	}

	/**
	 * @param CatalogTree $catalogTree
	 * @param array $must
	 * @return array
	 */
	private function addInPath(CatalogTree $catalogTree, array $must): array
	{
		$must[] = new Query\Term(['path' => $catalogTree->id]);
		return $must;
	}


	public function findByCategoryAndFromPrice(CatalogTree $category, float $priceLimit, PriceLevel $priceLevel, State $state, int $limit = 16): ResultSet
	{
		$query = new Query();
		$query->setSize($limit)
			->setFrom(0)
			->setSort([
				"topScore.{$state->code}.{$priceLevel->type}" => 'desc'
			]);

		$b = new QueryBuilder();

		$must = [];

		$must = $this->addOnlyPublic($b, $must);
		$must = $this->addInPath($category, $must);
		$must = $this->addIsInStore($must);

		$minPath = "statePricesWithVat.{$state->code}.{$priceLevel->type}";
		$must[] = new Range($minPath, ['gte'=> $priceLimit]);

		$queryBool = $b->query()->bool();
		foreach ($must as $item) {
			$queryBool->addMust($item);
		}

		$query->setQuery($queryBool);

		$esIndex = $this->esIndexRepository->getProductLastActive($category->mutation);
		return $this->baseSearch($esIndex, $query);
	}


	private function addIsInStore(array $must): array
	{
		$b = new QueryBuilder();

		$must[] = $b->query()->term(['isInStore' => true]);
		return $must;
	}

    public function findByCategoryAndMustArray(CatalogTree $category, array $must, Sort $esSort, int $limit = 16): ResultSet
    {
		$query = new Query();
		$query->setSize($limit)
			->setFrom(0)
			->setSort($esSort->getSentences());

		$b = new QueryBuilder();


		$must = $this->addOnlyPublic($b, $must);
		$must = $this->addInPath($category, $must);
//		$must = $this->addIsInStore($must);

		$queryBool = $b->query()->bool();
		foreach ($must as $item) {
			$queryBool->addMust($item);
		}

		$query->setQuery($queryBool);

		$esIndex = $this->esIndexRepository->getProductLastActive($category->mutation);
		return $this->baseSearch($esIndex, $query);

    }

	public function findByQuery(array $must, array $mustNot, Sort $esSort, Mutation $mutation, int $limit = 16, bool $useScoreFunction = false): ResultSet
	{

		$query = new Query();
		$query->setSize($limit)
			->setFrom(0);

		$b = new QueryBuilder();


		$must = $this->addOnlyPublic($b, $must);
//		$must = $this->addIsInStore($must);

		$queryBool = $b->query()->bool();
		foreach ($must as $item) {
			$queryBool->addMust($item);
		}
		foreach ($mustNot as $item) {
			$queryBool->addMustNot($item);
		}


		if ($useScoreFunction) {
			$functionScore = $b->query()->function_score();
			$functionScore->addRandomScoreFunction((int)Random::generate(10, '0-9'));
			$functionScore->setQuery($queryBool);
			$query->setQuery($functionScore);
		} else {
			$query->setQuery($queryBool);
		}

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->baseSearch($esIndex, $query);

	}

	public function findByIds(array $ids, Mutation $mutation, bool $onStockOnly, bool $inActionOnly = false, ?int $count = null, int $from = 0): ResultSet
	{
		$query = new Query();
		$b = new QueryBuilder();

		if (isset($count)) {
			$query->setFrom($from)->setSize($count);
		} else {
			$query->setSize(30);
		}

		$must = [];
		$must = $this->addOnlyPublic($b, $must);
		if ($onStockOnly) {
			$must = $this->addIsInStore($must);
		}
		if ($inActionOnly) {
			$must[] = $b->query()->term(['isAction' => true]);
		}
		$must = $this->addHasId($ids, $must);
		$must = $this->addInPath($mutation->pages->eshop, $must);

		$bool = $b->query()->bool();
		foreach ($must as $item) {
			$bool->addMust($item);
		}

		$query->setQuery($bool);

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->baseSearch($esIndex, $query);
	}


	private function addHasId(array $ids, array $must): array
	{
		$b = new QueryBuilder();

		$must[] = $b->query()->terms('id', array_values($ids));
		return $must;
	}

}
