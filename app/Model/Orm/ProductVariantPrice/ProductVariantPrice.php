<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property int $productId zatim neni treba relace
 * @property float $price {default 0.0}
 *
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$productVariantPrices} {default 1}
 * @property ProductVariant $productVariant {m:1 ProductVariant::$prices}
 * @property PriceLevel $priceLevel {m:1 PriceLevel::$prices}
 * @property DiscountPrice[]|OneHasMany $discountPrices {1:m DiscountPrice::$productVariantPrice}
 * @property ProductVariantWatchdog[]|OneHasMany $watchdogs {1:m ProductVariantWatchdog::$productVariantPrice}
 *
 * VIRTUALS
 * @property-read DiscountPrice|null $bestActiveDiscountPrice {virtual}
 */
class ProductVariantPrice extends Entity
{

	public const PRICE_ROUND_PRECISION = 5;

	private DiscountRepository $discountRepository;

	private ProductVariantWatchdogModel $productVariantWatchdogModel;
	private ProductVariantWatchdogRepository $productVariantWatchdogRepository;

	public function injectDiscountRepository(DiscountRepository $discountRepository): void
	{
		$this->discountRepository = $discountRepository;
	}

	public function injectProductVariantWatchdogModel(ProductVariantWatchdogModel $productVariantWatchdogModel): void
	{
		$this->productVariantWatchdogModel = $productVariantWatchdogModel;
	}

	public function injectProductVariantWatchdogRepository(ProductVariantWatchdogRepository $productVariantWatchdogRepository): void
	{
		$this->productVariantWatchdogRepository = $productVariantWatchdogRepository;
	}

	protected function getterBestActiveDiscountPrice(): ?DiscountPrice
	{
		$onlyActiveDiscounts = $this->discountRepository->getPublicOnlyWhereParams('discount->');

		return $this->discountPrices
			->toCollection()
			->findBy($onlyActiveDiscounts)
			->orderBy('price', ICollection::ASC)->fetch();
	}

	public function onAfterUpdate(): void
	{
		foreach ($this->watchdogs as $watchdog) {
			if ($watchdog->targetPrice > $this->price) {
				$this->productVariantWatchdogModel->send($watchdog, $this->mutation, null, 'watchdogPriceUnder');
				$this->productVariantWatchdogRepository->remove($watchdog);
			}
		}
		$this->productVariantWatchdogRepository->flush();
	}
}
