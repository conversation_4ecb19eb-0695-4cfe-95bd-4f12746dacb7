<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method array findRelatedProductIds(array $productIdsToCheck)
 */
final class ProductVariantWatchdogRepository extends Repository
{
    public static function getEntityClassNames(): array
    {
        return [ProductVariantWatchdog::class];
    }
	public function findActiveByProductVariant(ProductVariant $productVariant): ICollection
	{
		return $this->findBy([
			'productVariant' => $productVariant,
			'active' => 1,
		]);
	}

	public function findActiveByType(string $type): ICollection
	{
		return $this->findBy([
			'type' => $type,
			'active' => 1,
		]);
	}

	public function findProductIds(string|array $type): array
	{
		return $this->findBy(['type' => $type])->fetchPairs('productVariant->product->id', 'productVariant->product->id');
	}
}
