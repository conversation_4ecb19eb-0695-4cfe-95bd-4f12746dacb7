<?php declare(strict_types=1);

namespace App\Model;


use SuperKoderi\ConfigService;
use SuperKoderi\Email\Common;
use SuperKoderi\Email\ICommonFactory;
use SuperKoderi\LinkFactory;

class ProductVariantWatchdogModel
{
	private readonly Common $mailer;

	public function __construct(
		private readonly LinkFactory $linkFactory,
		ICommonFactory $commonEmailFactory,
		private readonly ConfigService $configService,
		private readonly Orm $orm,
	)
	{
		$this->mailer = $commonEmailFactory->create();
	}


	public function send(ProductVariantWatchdog $watchdog, Mutation $mutation, ?string $from = null, string $template = 'watchdog'): void
	{
		$from = $from ?? $this->configService->get('watchdog', 'notificationEmail');

		$productLocalization = $watchdog->productVariant->product->getLocalization($mutation);

		if(!$productLocalization) {
			throw new \LogicException("Missing product localization for mutation {$mutation->langCode}");
		}

		$this->mailer->send($from, $watchdog->email, $template, [
			'name' => $productLocalization->name,
			'url'  => $this->linkFactory->linkTranslateToNette($productLocalization),
		]);
	}

	public function sendRelated(ProductVariantWatchdog $watchdog, Mutation $mutation, int $productId, ?string $from = null, ?Common $mail = null): void
	{
		$mailer = $mail ?? $this->mailer;
		$from = $from ?? $this->configService->get('watchdog', 'notificationEmail');

		/** @var ProductLocalization $productLocalization */
		$productLocalization = $this->orm->productLocalization->getBy(['product->id' => $productId, 'mutation' => $mutation]);

		if(!$productLocalization) {
			throw new \LogicException("Missing product localization for mutation {$mutation->langCode}");
		}

		$mailer->send($from, $watchdog->email, 'watchdogRelated', [
			'name' => $productLocalization->name,
			'url'  => $this->linkFactory->linkTranslateToNette($productLocalization),
		]);
	}
}
