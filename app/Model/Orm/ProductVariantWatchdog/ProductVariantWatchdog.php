<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property ProductVariant $productVariant {m:1 ProductVariant::$watchdogs}
 * @property ProductVariantPrice|null $productVariantPrice {m:1 ProductVariantPrice::$watchdogs}
 * @property string $email
 * @property string $type {enum self::TYPE_*}
 * @property float|null $targetPrice
 * @property bool $active {default true}
 * @property DateTimeImmutable $createdAt {default now}
 *
 * VIRTUAL
 */
class ProductVariantWatchdog extends Entity
{
    public const TYPE_IN_STOCK = 'in_stock';
    public const TYPE_ACTION_PRICE = 'action_price';
    public const TYPE_PRICE_UNDER = 'price_under';
}
