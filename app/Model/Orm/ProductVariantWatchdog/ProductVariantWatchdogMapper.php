<?php declare(strict_types = 1);

namespace App\Model;

use App\Tratis\Orm\hasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class ProductVariantWatchdogMapper extends DbalMapper
{
    use hasCamelCase;

    protected $tableName = 'product_variant_watchdog';



	public function findRelatedProductIds(array $productIdsToCheck): array
	{
		return $this->connection->query('SELECT attachedProductId, mainProductId FROM product_product WHERE `type` = %s AND mainProductId IN %i[]', ProductProduct::TYPE_NORMAL, $productIdsToCheck)
			->fetchPairs('attachedProductId', 'mainProductId');
	}

}
