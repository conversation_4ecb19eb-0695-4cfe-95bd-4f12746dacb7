CREATE TABLE IF NOT EXISTS `product_variant_watchdog` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `productVariantId` int(11) NOT NULL,
    `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `type` enum('in_stock','action_price','price_under') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `targetPrice` decimal(10,2) NULL,
    `active` tinyint(1) NOT NULL DEFAULT 1,
    `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `FK_product_variant_watchdog_variant` (`productVariantId`) USING BTREE,
    KEY `idx_product_variant_watchdog_active` (`active`) USING BTREE,
    KEY `idx_product_variant_watchdog_type` (`type`) USING BTREE,
    KEY `idx_product_variant_watchdog_email` (`email`) USING BTREE,
    CONSTRAINT `FK_product_variant_watchdog_variant` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;

ALTER TABLE `product_variant_watchdog`
    ADD `productVariantPriceId` bigint(20) NULL,
ADD FOREIGN KEY (`productVariantPriceId`) REFERENCES `product_variant_price` (`id`);
